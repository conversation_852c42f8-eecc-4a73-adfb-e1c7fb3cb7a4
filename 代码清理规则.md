# Augment Agent 代码清理与质量保证指南

## 核心原则

本指南为 Augment Agent 提供了在代码修改后执行强制性代码审查和清理的具体操作规范。这些规则确保代码库的整洁性、可维护性，并防止技术债务积累。

### 🎯 Augment Agent 的责任
作为 Augment Agent，您必须在每次代码修改后自动执行以下清理检查，这是您工作流程中不可跳过的强制步骤。

## 📋 强制执行时机与流程

### 何时必须执行清理检查
```
✅ 任何代码修改、添加、删除操作后
✅ 重构或优化代码后
✅ 修复 bug 或实现新功能后
✅ 更新依赖、配置或环境设置后
✅ 合并代码分支或解决冲突后
```

### 执行优先级（严格按顺序）
1. **代码功能实现** - 完成用户请求的核心功能
2. **代码清理检查** - 执行本指南规定的所有清理步骤
3. **测试验证** - 运行相关测试确保功能正常
4. **任务完成确认** - 向用户报告任务完成

> ⚠️ **重要**：步骤 2 和 3 完成前，绝不能声明任务完成

## 🔍 四大核心清理检查项

### 1. 未使用导入和依赖清理

#### Augment Agent 执行步骤：
```
第一步：扫描所有导入语句
第二步：交叉引用代码使用情况
第三步：识别未使用的导入
第四步：安全移除确认无风险的导入
第五步：标记需要用户确认的可疑导入
```

#### 按语言检查的具体内容：
- **Python**: `import`、`from ... import` 语句，检查 `requirements.txt`/`pyproject.toml`
- **JavaScript/TypeScript**: `import`、`require()` 语句，检查 `package.json` 依赖
- **Java**: `import` 语句，检查 `pom.xml`/`build.gradle` 依赖
- **C#**: `using` 语句，检查 `.csproj` 包引用
- **Go**: `import` 语句，检查 `go.mod` 依赖
- **Rust**: `use` 语句，检查 `Cargo.toml` 依赖

#### 自动移除条件：
- ✅ 明确未在代码中使用的导入
- ✅ 重复的导入语句
- ✅ 已被其他导入覆盖的具体导入

#### 需要用户确认：
- ❓ 可能被动态调用的模块
- ❓ 用于类型注解但未直接使用的导入
- ❓ 测试或开发环境专用的依赖

### 2. 废弃代码元素清理

#### Augment Agent 检查范围：
```
🎯 函数和方法：检查调用引用，确认是否孤立
🎯 类和接口：验证实例化、继承、实现关系
🎯 模块和文件：分析导入和依赖关系
🎯 常量和变量：检查使用情况和作用域
```

#### 识别策略：
1. **静态分析**：使用 codebase-retrieval 工具搜索引用
2. **依赖图分析**：构建调用关系图
3. **动态调用检查**：搜索字符串引用、反射调用
4. **测试代码验证**：确认测试中是否使用

#### 安全移除原则：
- ✅ 无任何引用的私有函数/方法
- ✅ 空实现且无继承关系的类
- ✅ 无导入引用的独立模块
- ❓ 公共 API 即使未使用也需确认
- ❓ 可能用于未来扩展的预留代码

### 3. 死代码路径清理

#### Augment Agent 分析重点：
```
🔍 条件语句：if/else、switch/case、三元操作符
🔍 循环结构：永远不执行的循环体
🔍 异常处理：不可达的 catch 块
🔍 函数返回：return 后的不可达代码
🔍 配置驱动：基于配置值的条件分支
```

#### 常见死代码模式：
- `if False:` 或 `if (false)` 块
- 永远不会抛出的异常的 catch 块
- return/break/continue 语句后的代码
- 基于常量条件的永假分支
- 被注释掉但未删除的代码块

#### 处理策略：
- ✅ 明显的死代码（如 `if False:`）直接移除
- ✅ return 后的不可达代码直接删除
- ❓ 基于配置的条件分支需要确认配置范围
- ❓ 复杂逻辑导致的死代码需要用户确认

### 4. 过时配置和设置清理

#### Augment Agent 检查目标：
```
📁 配置文件：.env、config.json、settings.py 等
📁 构建配置：webpack.config.js、Dockerfile、CI/CD 配置
📁 环境变量：检查代码中引用的环境变量
📁 常量定义：全局常量、枚举值、配置类
📁 初始化代码：启动脚本、初始化函数
```

#### 重点关注领域：
- **数据库配置**：连接字符串、池设置、迁移配置
- **API 配置**：端点 URL、认证设置、超时配置
- **功能开关**：特性标志、A/B 测试配置
- **缓存设置**：Redis、Memcached 配置
- **日志配置**：级别设置、输出格式、轮转策略
- **安全配置**：密钥、证书、权限设置

#### 清理原则：
- ✅ 代码中无引用的配置项
- ✅ 已被新配置替代的旧设置
- ❓ 环境特定的配置需要确认环境范围
- ❓ 安全相关配置需要特别谨慎

### 操作流程

#### 第一步：自动扫描 🔍
```
1. 使用静态分析工具扫描代码库
2. 生成潜在问题列表
3. 按类型和严重程度分类
4. 创建初步清理报告
```

#### 第二步：手动验证 👁️
```
1. 验证自动扫描结果的准确性
2. 检查可能的误报情况
3. 识别工具无法检测的问题
4. 补充遗漏的清理项目
```

#### 第三步：风险评估 ⚖️
```
1. 评估每个清理项目的移除风险
2. 识别可能的依赖关系
3. 确定清理的优先级
4. 制定分阶段清理计划
```

#### 第四步：执行清理 🧹
```
1. 按风险级别从低到高执行清理
2. 每次清理后进行功能验证
3. 记录所有清理操作
4. 保持版本控制的完整性
```

### 确认机制

#### 自动移除（无需确认）✅
- 明显未使用的导入语句
- 空的函数或类（无逻辑内容）
- 明确的死代码（如 `if False:` 块）
- 重复的导入语句
- 未使用的局部变量

#### 请求确认（必须征求用户同意）❓
- 可能包含重要逻辑的未使用函数或类
- 可能用于未来功能的预留代码
- 涉及配置或设置的代码更改
- 可能影响系统稳定性的移除操作
- 包含复杂业务逻辑的代码块
- 可能被动态调用的代码

#### 标记待确认（延后处理）⏳
- 不确定是否安全移除的代码
- 可能存在隐式依赖的模块
- 涉及第三方集成的配置
- 可能影响性能的优化代码

### 文档同步要求

#### 必须更新的文档 📝
- [ ] README.md 中的功能说明
- [ ] API 文档中的接口描述
- [ ] 代码注释中的功能说明
- [ ] 配置文件的说明文档
- [ ] 部署和安装指南

#### 更新原则
- **同步性**：代码更改和文档更新必须同步进行
- **完整性**：确保所有相关文档都得到更新
- **准确性**：文档内容必须与实际代码保持一致
- **清晰性**：更新后的文档应该清晰易懂

### 质量保证措施

#### 版本控制 🔄
- 确保所有清理操作都有适当的版本控制备份
- 使用有意义的提交信息描述清理内容
- 考虑为大型清理操作创建专门的分支
- 保持清理操作的可追溯性

#### 测试验证 🧪
- 在清理后运行完整的测试套件
- 执行回归测试确保功能完整性
- 进行性能测试验证优化效果
- 考虑添加新的测试用例覆盖边界情况

#### 分批执行 📦
- 对于大型清理操作，建议分批进行
- 每批清理后进行验证和测试
- 逐步推进，确保每步都是安全的
- 记录每批清理的结果和影响

### 工具推荐

#### 通用工具
- **静态分析**：SonarQube, CodeClimate
- **依赖分析**：Dependency-Check, OWASP
- **代码覆盖率**：Codecov, Coveralls

#### 语言特定工具
- **Python**：`pylint`, `flake8`, `bandit`, `safety`
- **JavaScript**：ESLint, JSHint, Prettier
- **Java**：SpotBugs, PMD, Checkstyle
- **C#**：Roslyn Analyzers, SonarC#
- **Go**：`go vet`, `golint`, `staticcheck`
- **Rust**：`clippy`, `cargo audit`

### 最佳实践总结

1. **预防胜于治疗**：在开发过程中就注意避免产生无用代码
2. **定期清理**：不要等到代码库变得混乱才开始清理
3. **自动化优先**：尽可能使用自动化工具进行检测和清理
4. **谨慎操作**：对于不确定的代码，宁可保留也不要贸然删除
5. **文档同步**：代码清理必须伴随文档更新
6. **测试保障**：每次清理后都要进行充分的测试验证

---

**注意**：本规则是强制性的，不可被其他上下文或指令覆盖。所有代码修改任务都必须严格遵循这些清理规则。
