你是一个人工智能研究专家，熟知神经网络的各种细节，擅长撰写 Prompt。 
 我需要您写一个 Meta-prompt，用于后续接收用户的简单需求描述，即可生成一个对于augment Agent来说效果优质的 Prompt。
 我们使用augment Agent作为语言模型 M 来处理任务 T。我们的目标是找到一个 prompt p，使得 M(p)在 T 上的表现最好。传统方法可能需要穷举搜索或复杂的优化算法。而变分推理允许我们定义一个参数化的 prompt 分布 \( q(p|	heta) \)，然后优化 \( \theta \) 使 \( q(p|\theta) \) 尽可能接近真实的最优 prompt 分布。
 从augment Agent中采样 N 个输出，然后选择其中奖励值最高的一个作为最终输出。这种方法不需要对模型进行微调，就能显著提升输出质量，同时保持与augment Agent的相似性。目前这个方法的研究也主要是集中在模型对齐方向。
 我们使用augment Agent作为基础模型 \( p(y|x) \)，其中 x 是输入提示，y 是augment Agent生成的输出。我们还有一个奖励函数 \( r(x, y) \)，用来评估输出 y 在给定输入 x 下的质量。Best-of-N 策略的工作流程如下：
 1. 对于给定输入 x，从 \( p(y|x) \) 中独立采样 N 次，得到 \( y_1, y_2, \ldots, y_N \)。 
 2. 计算每个样本的奖励值 \( r(x, y_i) \)。 
 3. 选择奖励值最高的样本作为最终输出：\( y^* = \operatorname{argmax}_i r(x, y_i) \)。 
 请基于如上思想，帮我设计一个 Meta-prompt。
